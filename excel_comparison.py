import pandas as pd
import sys
import os

def main():
    print("=== Excel Sheet Comparison Tool ===")
    
    file_path = "Classeur1.xlsx"
    
    # Check if file exists
    if not os.path.exists(file_path):
        print(f"❌ File {file_path} not found!")
        return False
    
    print(f"✅ File found: {file_path}")
    print(f"📊 File size: {os.path.getsize(file_path)} bytes")
    
    try:
        # Try different engines
        engines = ['openpyxl', 'xlrd']
        excel_data = None
        
        for engine in engines:
            try:
                print(f"🔄 Trying to read with engine: {engine}")
                excel_data = pd.ExcelFile(file_path, engine=engine)
                print(f"✅ Successfully opened with {engine}")
                break
            except Exception as e:
                print(f"❌ Failed with {engine}: {str(e)[:100]}...")
                continue
        
        if excel_data is None:
            print("❌ Could not open file with any engine")
            return False
        
        # Get sheet names
        sheet_names = excel_data.sheet_names
        print(f"📋 Available sheets: {sheet_names}")
        
        # Check for target sheets
        target_sheets = ["SANTSDM test", "SDMTESTP test"]
        available_target_sheets = [sheet for sheet in target_sheets if sheet in sheet_names]
        
        if len(available_target_sheets) < 2:
            print(f"❌ Required sheets not found!")
            print(f"   Looking for: {target_sheets}")
            print(f"   Found: {available_target_sheets}")
            return False
        
        print(f"✅ Found target sheets: {available_target_sheets}")
        
        # Read the sheets
        sheets_data = {}
        for sheet_name in available_target_sheets:
            try:
                print(f"📖 Reading sheet: {sheet_name}")
                df = pd.read_excel(file_path, sheet_name=sheet_name, engine=excel_data.engine)
                sheets_data[sheet_name] = df
                print(f"   Shape: {df.shape}")
                print(f"   Columns: {list(df.columns)[:5]}...")  # Show first 5 columns
            except Exception as e:
                print(f"❌ Error reading {sheet_name}: {e}")
                return False
        
        # Perform comparison
        sheet1_name = available_target_sheets[0]
        sheet2_name = available_target_sheets[1]
        
        df1 = sheets_data[sheet1_name]
        df2 = sheets_data[sheet2_name]
        
        print(f"\n🔍 Comparing sheets...")
        print(f"   {sheet1_name}: {df1.shape[0]} rows")
        print(f"   {sheet2_name}: {df2.shape[0]} rows")
        
        # Check for required columns
        required_columns = ["Summary", "Issue key"]
        
        # Find actual column names (case insensitive)
        def find_column(df, target_col):
            for col in df.columns:
                if str(col).lower().strip() == target_col.lower().strip():
                    return col
            return None
        
        actual_columns = {}
        for sheet_name, df in sheets_data.items():
            actual_columns[sheet_name] = {}
            for req_col in required_columns:
                found_col = find_column(df, req_col)
                if found_col:
                    actual_columns[sheet_name][req_col] = found_col
                else:
                    print(f"❌ Column '{req_col}' not found in {sheet_name}")
                    print(f"   Available columns: {list(df.columns)}")
                    return False
        
        print("✅ All required columns found")
        
        # Extract data for comparison
        def extract_comparison_data(df, sheet_name):
            summary_col = actual_columns[sheet_name]["Summary"]
            issue_key_col = actual_columns[sheet_name]["Issue key"]
            
            # Create a clean dataframe with just the required columns
            clean_df = df[[summary_col, issue_key_col]].copy()
            clean_df.columns = ["Summary", "Issue key"]  # Standardize column names
            
            # Remove rows with NaN values
            clean_df = clean_df.dropna()
            
            # Create a unique key for comparison
            clean_df['comparison_key'] = clean_df['Summary'].astype(str) + '|' + clean_df['Issue key'].astype(str)
            
            return clean_df
        
        df1_clean = extract_comparison_data(df1, sheet1_name)
        df2_clean = extract_comparison_data(df2, sheet2_name)
        
        print(f"📊 Clean data:")
        print(f"   {sheet1_name}: {len(df1_clean)} valid rows")
        print(f"   {sheet2_name}: {len(df2_clean)} valid rows")
        
        # Perform comparison
        set1 = set(df1_clean['comparison_key'])
        set2 = set(df2_clean['comparison_key'])
        
        only_in_sheet1 = set1 - set2
        only_in_sheet2 = set2 - set1
        in_both = set1 & set2
        
        print(f"\n📈 Comparison Results:")
        print(f"   Items only in {sheet1_name}: {len(only_in_sheet1)}")
        print(f"   Items only in {sheet2_name}: {len(only_in_sheet2)}")
        print(f"   Items in both sheets: {len(in_both)}")
        
        # Create results dataframe
        results = []
        
        # Add items only in sheet1
        for key in only_in_sheet1:
            row = df1_clean[df1_clean['comparison_key'] == key].iloc[0]
            results.append({
                'Summary': row['Summary'],
                'Issue key': row['Issue key'],
                'Status': f'Only in {sheet1_name}',
                'Source Sheet': sheet1_name
            })
        
        # Add items only in sheet2
        for key in only_in_sheet2:
            row = df2_clean[df2_clean['comparison_key'] == key].iloc[0]
            results.append({
                'Summary': row['Summary'],
                'Issue key': row['Issue key'],
                'Status': f'Only in {sheet2_name}',
                'Source Sheet': sheet2_name
            })
        
        # Add items in both
        for key in in_both:
            row = df1_clean[df1_clean['comparison_key'] == key].iloc[0]
            results.append({
                'Summary': row['Summary'],
                'Issue key': row['Issue key'],
                'Status': 'In both sheets',
                'Source Sheet': 'Both'
            })
        
        results_df = pd.DataFrame(results)
        
        # Create summary
        summary_df = pd.DataFrame({
            'Metric': [
                f'Total items in {sheet1_name}',
                f'Total items in {sheet2_name}',
                f'Items only in {sheet1_name}',
                f'Items only in {sheet2_name}',
                'Items in both sheets',
                'Total unique items'
            ],
            'Count': [
                len(df1_clean),
                len(df2_clean),
                len(only_in_sheet1),
                len(only_in_sheet2),
                len(in_both),
                len(results_df)
            ]
        })
        
        print(f"\n📋 Summary:")
        print(summary_df.to_string(index=False))
        
        # Save to new sheet
        try:
            comparison_sheet_name = "Comparison Results"
            
            # Create a new Excel writer
            with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                # Write summary at the top
                summary_df.to_excel(writer, sheet_name=comparison_sheet_name, index=False, startrow=0)
                
                # Write detailed results below
                results_df.to_excel(writer, sheet_name=comparison_sheet_name, index=False, startrow=len(summary_df) + 3)
            
            print(f"✅ Results saved to sheet '{comparison_sheet_name}'")
            
        except Exception as e:
            print(f"❌ Error saving results: {e}")
            # Save to CSV as backup
            results_df.to_csv('comparison_results.csv', index=False)
            summary_df.to_csv('comparison_summary.csv', index=False)
            print("💾 Results saved to CSV files instead")
        
        return True
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Comparison completed successfully!")
    else:
        print("\n💥 Comparison failed!")
