import pandas as pd
import os
import glob

def find_data_files():
    """Find available data files in the directory"""
    patterns = ['*.xlsx', '*.xls', '*.csv']
    files = []
    for pattern in patterns:
        files.extend(glob.glob(pattern))
    return files

def compare_csv_files():
    """Compare data from CSV files"""
    print("=== CSV Comparison Tool ===")
    
    # Look for CSV files
    csv_files = glob.glob('*.csv')
    
    if len(csv_files) < 2:
        print(f"❌ Need at least 2 CSV files. Found: {csv_files}")
        return False
    
    print(f"📁 Found CSV files: {csv_files}")
    
    # Let user choose or auto-select first two
    if len(csv_files) >= 2:
        file1 = csv_files[0]
        file2 = csv_files[1]
        
        print(f"📊 Comparing: {file1} vs {file2}")
        
        try:
            df1 = pd.read_csv(file1)
            df2 = pd.read_csv(file2)
            
            print(f"✅ {file1}: {df1.shape}")
            print(f"✅ {file2}: {df2.shape}")
            
            # Show columns
            print(f"📋 {file1} columns: {list(df1.columns)}")
            print(f"📋 {file2} columns: {list(df2.columns)}")
            
            # Look for Summary and Issue key columns
            required_columns = ["Summary", "Issue key"]
            
            def find_column(df, target_col):
                for col in df.columns:
                    if target_col.lower() in str(col).lower():
                        return col
                return None
            
            cols1 = {req: find_column(df1, req) for req in required_columns}
            cols2 = {req: find_column(df2, req) for req in required_columns}
            
            print(f"🔍 Found columns in {file1}: {cols1}")
            print(f"🔍 Found columns in {file2}: {cols2}")
            
            if None in cols1.values() or None in cols2.values():
                print("❌ Required columns not found")
                return False
            
            # Perform comparison
            df1_subset = df1[[cols1["Summary"], cols1["Issue key"]]].dropna()
            df2_subset = df2[[cols2["Summary"], cols2["Issue key"]]].dropna()
            
            df1_subset.columns = ["Summary", "Issue key"]
            df2_subset.columns = ["Summary", "Issue key"]
            
            # Create comparison keys
            df1_subset['key'] = df1_subset['Summary'].astype(str) + '|' + df1_subset['Issue key'].astype(str)
            df2_subset['key'] = df2_subset['Summary'].astype(str) + '|' + df2_subset['Issue key'].astype(str)
            
            set1 = set(df1_subset['key'])
            set2 = set(df2_subset['key'])
            
            only_in_1 = set1 - set2
            only_in_2 = set2 - set1
            in_both = set1 & set2
            
            print(f"\n📈 Results:")
            print(f"   Only in {file1}: {len(only_in_1)}")
            print(f"   Only in {file2}: {len(only_in_2)}")
            print(f"   In both: {len(in_both)}")
            
            # Create results
            results = []
            
            for key in only_in_1:
                row = df1_subset[df1_subset['key'] == key].iloc[0]
                results.append({
                    'Summary': row['Summary'],
                    'Issue key': row['Issue key'],
                    'Status': f'Only in {file1}',
                    'Source': file1
                })
            
            for key in only_in_2:
                row = df2_subset[df2_subset['key'] == key].iloc[0]
                results.append({
                    'Summary': row['Summary'],
                    'Issue key': row['Issue key'],
                    'Status': f'Only in {file2}',
                    'Source': file2
                })
            
            for key in in_both:
                row = df1_subset[df1_subset['key'] == key].iloc[0]
                results.append({
                    'Summary': row['Summary'],
                    'Issue key': row['Issue key'],
                    'Status': 'In both files',
                    'Source': 'Both'
                })
            
            results_df = pd.DataFrame(results)
            
            # Save results
            results_df.to_csv('comparison_results.csv', index=False)
            print(f"💾 Results saved to comparison_results.csv")
            
            # Show summary
            print(f"\n📊 Summary saved to comparison_results.csv:")
            print(f"   Total comparisons: {len(results_df)}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False

def manual_comparison():
    """Manual comparison tool for when files need to be specified"""
    print("\n=== Manual Comparison Instructions ===")
    print("If your Excel file is corrupted, please:")
    print("1. Open the Excel file in Microsoft Excel")
    print("2. Go to each sheet ('SANTSDM test' and 'SDMTESTP test')")
    print("3. Select all data (Ctrl+A)")
    print("4. Copy and paste into new CSV files:")
    print("   - Save first sheet as: santsdm_test.csv")
    print("   - Save second sheet as: sdmtestp_test.csv")
    print("5. Run this script again")
    
    return False

if __name__ == "__main__":
    # First try to find and list all data files
    data_files = find_data_files()
    print(f"📁 Available data files: {data_files}")
    
    if not data_files:
        print("❌ No data files found")
        manual_comparison()
    elif 'Classeur1.xlsx' in data_files:
        print("⚠️  Excel file found but appears corrupted")
        manual_comparison()
    else:
        success = compare_csv_files()
        if not success:
            manual_comparison()
