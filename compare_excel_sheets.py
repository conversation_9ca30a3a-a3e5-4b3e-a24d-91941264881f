import pandas as pd
import openpyxl
from openpyxl import Workbook
import sys

def compare_excel_sheets(file_path):
    """
    Compare two Excel sheets and create a comparison result in a third sheet
    """
    try:
        # Read the Excel file
        print(f"Opening Excel file: {file_path}")
        
        # Load the workbook to check sheet names
        wb = openpyxl.load_workbook(file_path)
        print(f"Available sheets: {wb.sheetnames}")
        
        # Read the two sheets to compare
        sheet1_name = "SANTSDM test"
        sheet2_name = "SDMTESTP test"
        
        # Check if sheets exist
        if sheet1_name not in wb.sheetnames:
            print(f"Sheet '{sheet1_name}' not found!")
            return
        if sheet2_name not in wb.sheetnames:
            print(f"Sheet '{sheet2_name}' not found!")
            return
        
        # Read the sheets using pandas
        df1 = pd.read_excel(file_path, sheet_name=sheet1_name)
        df2 = pd.read_excel(file_path, sheet_name=sheet2_name)
        
        print(f"Sheet 1 ({sheet1_name}) columns: {list(df1.columns)}")
        print(f"Sheet 1 shape: {df1.shape}")
        print(f"Sheet 2 ({sheet2_name}) columns: {list(df2.columns)}")
        print(f"Sheet 2 shape: {df2.shape}")
        
        # Check if required columns exist
        required_columns = ["Summary", "Issue key"]
        
        for col in required_columns:
            if col not in df1.columns:
                print(f"Column '{col}' not found in {sheet1_name}")
                print(f"Available columns: {list(df1.columns)}")
                return
            if col not in df2.columns:
                print(f"Column '{col}' not found in {sheet2_name}")
                print(f"Available columns: {list(df2.columns)}")
                return
        
        # Extract only the required columns
        df1_subset = df1[required_columns].copy()
        df2_subset = df2[required_columns].copy()
        
        # Remove rows with NaN values in key columns
        df1_subset = df1_subset.dropna(subset=required_columns)
        df2_subset = df2_subset.dropna(subset=required_columns)
        
        print(f"After cleaning - Sheet 1: {len(df1_subset)} rows, Sheet 2: {len(df2_subset)} rows")
        
        # Create comparison results
        comparison_results = []
        
        # Find items in sheet1 but not in sheet2
        df1_subset['key'] = df1_subset['Summary'].astype(str) + '|' + df1_subset['Issue key'].astype(str)
        df2_subset['key'] = df2_subset['Summary'].astype(str) + '|' + df2_subset['Issue key'].astype(str)
        
        # Items only in sheet 1
        only_in_sheet1 = df1_subset[~df1_subset['key'].isin(df2_subset['key'])].copy()
        only_in_sheet1['Status'] = f'Only in {sheet1_name}'
        only_in_sheet1['Source Sheet'] = sheet1_name
        
        # Items only in sheet 2
        only_in_sheet2 = df2_subset[~df2_subset['key'].isin(df1_subset['key'])].copy()
        only_in_sheet2['Status'] = f'Only in {sheet2_name}'
        only_in_sheet2['Source Sheet'] = sheet2_name
        
        # Items in both sheets
        common_items = df1_subset[df1_subset['key'].isin(df2_subset['key'])].copy()
        common_items['Status'] = 'In both sheets'
        common_items['Source Sheet'] = 'Both'
        
        # Combine all results
        all_results = []
        
        if not only_in_sheet1.empty:
            all_results.append(only_in_sheet1[['Summary', 'Issue key', 'Status', 'Source Sheet']])
        
        if not only_in_sheet2.empty:
            all_results.append(only_in_sheet2[['Summary', 'Issue key', 'Status', 'Source Sheet']])
        
        if not common_items.empty:
            all_results.append(common_items[['Summary', 'Issue key', 'Status', 'Source Sheet']])
        
        if all_results:
            comparison_df = pd.concat(all_results, ignore_index=True)
        else:
            comparison_df = pd.DataFrame(columns=['Summary', 'Issue key', 'Status', 'Source Sheet'])
        
        # Add summary statistics
        summary_stats = pd.DataFrame({
            'Metric': [
                f'Total items in {sheet1_name}',
                f'Total items in {sheet2_name}',
                f'Items only in {sheet1_name}',
                f'Items only in {sheet2_name}',
                'Items in both sheets'
            ],
            'Count': [
                len(df1_subset),
                len(df2_subset),
                len(only_in_sheet1),
                len(only_in_sheet2),
                len(common_items)
            ]
        })
        
        print("\nComparison Summary:")
        print(summary_stats.to_string(index=False))
        
        # Save results to a new sheet
        comparison_sheet_name = "Comparison Results"
        
        # Create a new workbook with the comparison results
        with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
            # Write summary statistics
            summary_stats.to_excel(writer, sheet_name=comparison_sheet_name, index=False, startrow=0)
            
            # Write detailed comparison results
            comparison_df.to_excel(writer, sheet_name=comparison_sheet_name, index=False, startrow=len(summary_stats) + 3)
        
        print(f"\nComparison results saved to sheet '{comparison_sheet_name}'")
        print(f"Total comparison items: {len(comparison_df)}")
        
        return comparison_df, summary_stats
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    file_path = "Classeur1.xlsx"
    compare_excel_sheets(file_path)
