import pandas as pd
import os

print("Starting Excel analysis with pandas...")

file_path = "Classeur1.xlsx"
if not os.path.exists(file_path):
    print(f"File {file_path} not found!")
    exit(1)

print(f"File {file_path} found!")

try:
    # Try to read all sheets
    excel_file = pd.ExcelFile(file_path)
    print(f"Available sheets: {excel_file.sheet_names}")
    
    # Read each sheet
    for sheet_name in excel_file.sheet_names:
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"\nSheet '{sheet_name}':")
            print(f"  Shape: {df.shape}")
            print(f"  Columns: {list(df.columns)}")
            
            # Show first few rows
            if not df.empty:
                print(f"  First 3 rows:")
                print(df.head(3).to_string())
            
        except Exception as e:
            print(f"Error reading sheet '{sheet_name}': {e}")
            
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
