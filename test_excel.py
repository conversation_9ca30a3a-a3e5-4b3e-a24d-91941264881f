import pandas as pd
import openpyxl
import os

print("Starting Excel analysis...")

# Check if file exists
file_path = "Classeur1.xlsx"
if not os.path.exists(file_path):
    print(f"File {file_path} not found!")
    exit(1)

print(f"File {file_path} found!")

try:
    # Load workbook to check sheet names
    wb = openpyxl.load_workbook(file_path)
    print(f"Available sheets: {wb.sheetnames}")
    
    # Try to read each sheet
    for sheet_name in wb.sheetnames:
        try:
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            print(f"Sheet '{sheet_name}': {df.shape[0]} rows, {df.shape[1]} columns")
            print(f"Columns: {list(df.columns)}")
            print("---")
        except Exception as e:
            print(f"Error reading sheet '{sheet_name}': {e}")
            
except Exception as e:
    print(f"Error opening file: {e}")
    import traceback
    traceback.print_exc()
